<br />
<b>Fatal error</b>:  Uncaught UnhandledMatchError: Unhandled match value of type Monolog\Level in C:\xampp\htdocs\vendor\monolog\monolog\src\Monolog\Level.php:178
Stack trace:
#0 C:\xampp\htdocs\vendor\monolog\monolog\src\Monolog\LogRecord.php(124): Monolog\Level-&gt;getName()
#1 C:\xampp\htdocs\vendor\monolog\monolog\src\Monolog\Formatter\NormalizerFormatter.php(154): Monolog\LogRecord-&gt;toArray()
#2 C:\xampp\htdocs\vendor\monolog\monolog\src\Monolog\Formatter\NormalizerFormatter.php(51): Monolog\Formatter\NormalizerFormatter-&gt;normalizeRecord(Object(Monolog\LogRecord))
#3 C:\xampp\htdocs\vendor\monolog\monolog\src\Monolog\Formatter\LineFormatter.php(135): Monolog\Formatter\NormalizerFormatter-&gt;format(Object(Monolog\LogRecord))
#4 C:\xampp\htdocs\vendor\monolog\monolog\src\Monolog\Handler\AbstractProcessingHandler.php(42): Monolog\Formatter\LineFormatter-&gt;format(Object(Monolog\LogRecord))
#5 C:\xampp\htdocs\vendor\monolog\monolog\src\Monolog\Logger.php(389): Monolog\Handler\AbstractProcessingHandler-&gt;handle(Object(Monolog\LogRecord))
#6 C:\xampp\htdocs\vendor\monolog\monolog\src\Monolog\Logger.php(683): Monolog\Logger-&gt;addRecord(600, 'Unable to creat...', Array)
#7 C:\xampp\htdocs\vendor\laravel\framework\src\Illuminate\Log\Logger.php(186): Monolog\Logger-&gt;emergency('Unable to creat...', Array)
#8 C:\xampp\htdocs\vendor\laravel\framework\src\Illuminate\Log\Logger.php(61): Illuminate\Log\Logger-&gt;writeLog('emergency', 'Unable to creat...', Array)
#9 C:\xampp\htdocs\vendor\laravel\framework\src\Illuminate\Log\LogManager.php(143): Illuminate\Log\Logger-&gt;emergency('Unable to creat...', Array)
#10 C:\xampp\htdocs\vendor\laravel\framework\src\Illuminate\Support\helpers.php(307): Illuminate\Log\LogManager-&gt;Illuminate\Log\{closure}(Object(Illuminate\Log\Logger))
#11 C:\xampp\htdocs\vendor\laravel\framework\src\Illuminate\Log\LogManager.php(145): tap(Object(Illuminate\Log\Logger), Object(Closure))
#12 C:\xampp\htdocs\vendor\laravel\framework\src\Illuminate\Log\LogManager.php(124): Illuminate\Log\LogManager-&gt;get(NULL)
#13 C:\xampp\htdocs\vendor\laravel\framework\src\Illuminate\Log\LogManager.php(681): Illuminate\Log\LogManager-&gt;driver()
#14 C:\xampp\htdocs\vendor\laravel\framework\src\Illuminate\Foundation\Exceptions\Handler.php(317): Illuminate\Log\LogManager-&gt;error('syntax error, u...', Array)
#15 C:\xampp\htdocs\vendor\laravel\framework\src\Illuminate\Foundation\Exceptions\Handler.php(278): Illuminate\Foundation\Exceptions\Handler-&gt;reportThrowable(Object(ParseError))
#16 C:\xampp\htdocs\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(497): Illuminate\Foundation\Exceptions\Handler-&gt;report(Object(ParseError))
#17 C:\xampp\htdocs\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(146): Illuminate\Foundation\Http\Kernel-&gt;reportException(Object(ParseError))
#18 C:\xampp\htdocs\public\index.php(52): Illuminate\Foundation\Http\Kernel-&gt;handle(Object(Illuminate\Http\Request))
#19 {main}
  thrown in <b>C:\xampp\htdocs\vendor\monolog\monolog\src\Monolog\Level.php</b> on line <b>178</b><br />
